<template>
  <div v-if="detailData" class="instructionBox">
    <div class="topCont">
      <div class="desc">
        <div class="topic" :style="{ color: colorArray[detailData.workOrderStatus] }">{{ topicName }}</div>
        <div class="tag" :style="{ backgroundColor: colorArray[detailData.workOrderStatus] }">
          {{ detailData.workOrderStatus == 1 ? '已批示' : detailData.workOrderStatus == 2 ? '推进中' : '已办结' }}
        </div>
      </div>
      <div class="title">{{ detailData.workOrderTitle }}</div>
      <div class="id">编号: {{ detailData.id }}</div>
      <div class="img" @click="imageShow">
        <van-image-preview v-model="show" :images="[detailData.workOrderFile]" @change="onChange"> </van-image-preview>
        <img :src="detailData.workOrderFile" alt="" />
      </div>
    </div>
    <div class="myInstruction">
      <div class="topDesc">
        <div class="ltBg"><span>我的批示</span></div>
        <div class="time">{{ detailData.createTime }}</div>
      </div>
      <div class="text">{{ detailData.workOrderContent }}</div>
    </div>
    <!-- 推进中 -->
    <div v-if="detailData.workOrderStatus == '2' || detailData.workOrderStatus == '3'" class="propelling common_box">
      <div class="topDesc">
        <div class="title">工作要求</div>
        <div class="time">{{ detailData.supervisionTime }}</div>
      </div>
      <div class="text">{{ detailData.supervisionContent }}</div>
    </div>
    <!-- 已办结 -->
    <div v-if="detailData.workOrderStatus == '3'" class="finished common_box">
      <div class="topDesc">
        <div class="title">总结反馈<span>办结</span></div>
        <div class="time">{{ detailData?.replyList[0]?.createTime }}</div>
      </div>
      <div class="text">{{ detailData?.replyList[0]?.replyContent }}</div>
    </div>
  </div>
</template>

<script>
import { getAction, postAction } from '@/api/manage'

export default {
  data() {
    return {
      id: null,
      colorArray: {
        1: '#0c93fa',
        2: '#ffa029',
        3: '#31da8d'
      },
      detailData: null,
      show: false,
      index: 0
    }
  },
  mounted() {
    document.title = '批示详情'
    this.id = this.$route.query.id
    this.topicName = this.$route.query.topicName
    // this.userInfo = JSON.parse(localStorage.getItem('userInfo')).value
    this.loadData()
  },
  methods: {
    imageShow() {
      this.show = true
    },
    onChange(index) {
      this.index = index
    },
    loadData() {
      getAction('/cockpit/workorder/scCockpitWorkOrder/getWorkOrder', {
        id: this.id
      }).then((res) => {
        this.detailData = res.result
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.instructionBox {
  width: 100vw;
  height: 100vh;
  background: #f4f8fb;
  .topCont {
    padding: 26px;
    background: white;
    .desc {
      display: flex;
      align-items: center;
      justify-content: space-between;
      .topic {
        padding: 4px 8px;
        border-radius: 5px;
        background: #0290f919;
        color: #0290f9;
        font-family: PingFang SC;
        font-weight: bold;
        font-size: 26px;
        line-height: normal;
        letter-spacing: 0px;
        text-align: left;
      }
      .tag {
        padding: 6px 14px;
        margin-right: -26px;
        color: #ffffff;
        font-family: PingFang SC;
        font-weight: bold;
        font-size: 26px;
        line-height: normal;
        letter-spacing: 0px;
        text-align: left;
        border-radius: 100px 0px 0px 100px;
        // background: linear-gradient(-90deg, #34db8b 0%, #10d8a7 100%);
      }
    }
    .title {
      margin-top: 16px;
      color: #0e0d16;
      font-family: PingFang SC;
      font-weight: bold;
      font-size: 32px;
      line-height: normal;
      letter-spacing: 0px;
      text-align: left;
    }
    .id {
      color: #a3a3a3;
      margin-top: 16px;
      font-family: PingFang SC;
      font-weight: medium;
      font-size: 26px;
      line-height: 46px;
      letter-spacing: 0px;
      text-align: left;
    }
    .img {
      width: 156px;
      margin: 16px auto;
    }
  }
  .myInstruction {
    margin: 25px auto;
    background-color: white;
    border-radius: 10px;
    border: 2px solid #ffffff;
    width: 702px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 24px;
    box-shadow: 0px 4px 10px 0px #0000000c;
    .topDesc {
      display: flex;
      justify-content: space-between;
      align-items: center;
      .ltBg {
        background: url('@/views/home/<USER>/myinstr.png') center/100% 100% no-repeat;
        width: 335px;
        height: 162px;
        // border-radius: 50px;
        // background: linear-gradient(-90deg, #e0f1fe00 0%, #d4ecfe 100%);
        color: #0558e1;
        font-family: Tensentype ZhiHeiJ-W3;
        font-weight: regular;
        font-size: 30px;
        line-height: normal;
        letter-spacing: 0px;
        text-align: left;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: -80px 0 0 -30px;
        span {
          transform: translate(25px, 18px);
        }
      }
      .time {
        color: #a3a3a3;
        font-family: PingFang SC;
        font-weight: medium;
        font-size: 26px;
        line-height: normal;
        letter-spacing: 0px;
        text-align: left;
        transform: translate(0px, -15px);
      }
    }
    .text {
      color: #212121;
      font-family: PingFang SC;
      font-weight: medium;
      font-size: 28px;
      line-height: 44px;
      letter-spacing: 0px;
      text-align: left;
      //   display: -webkit-box;
      //   -webkit-box-orient: vertical;
      //   -webkit-line-clamp: ;
      //   overflow: hidden;
      //   text-overflow: ellipsis;
    }
  }
  .common_box {
    margin: 25px auto;
    background-color: white;
    border-radius: 10px;
    border: 2px solid #ffffff;
    width: 702px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 24px;
    box-shadow: 0px 4px 10px 0px #0000000c;
    .topDesc {
      display: flex;
      justify-content: space-between;
      align-items: center;
      .title {
        color: #212121;
        font-family: PingFang SC;
        font-weight: bold;
        font-size: 32px;
        line-height: normal;
        letter-spacing: 0px;
        text-align: left;
        span {
          vertical-align: middle;
          padding: 2px 8px;
          border-radius: 5px;
          background: #0290f919;
          color: #31da8d;
          font-family: PingFang SC;
          font-weight: bold;
          font-size: 24px;
          line-height: 24px;
          letter-spacing: 0px;
          text-align: left;
          margin-left: 10px;
        }
      }
      .title::before {
        content: '';
        display: inline-block;
        width: 16px;
        vertical-align: middle;
        margin-right: 10px;
        height: 35px;
        background: url('../img/title_before.png') center/100% 100% no-repeat;
      }
      .time {
        color: #a3a3a3;
        font-family: PingFang SC;
        font-weight: medium;
        font-size: 26px;
        line-height: normal;
        letter-spacing: 0px;
        text-align: left;
      }
    }
    .text {
      margin-top: 16px;
      color: #212121;
      font-family: PingFang SC;
      font-weight: medium;
      font-size: 28px;
      line-height: 44px;
      letter-spacing: 0px;
      text-align: left;
    }
  }
}
@media (min-width: 345px) {
  .instructionBox {
    .topCont {
      padding: 13px;
      .desc {
        .topic {
          padding: 2px 4px;
          border-radius: 2px;
          font-size: 13px;
        }
        .tag {
          padding: 3px 7px;
          margin-right: -13px;
          font-size: 13px;
          border-radius: 50px 0px 0px 50px;
        }
      }
      .title {
        margin-top: 8px;
        font-size: 16px;
      }
      .id {
        margin-top: 8px;
        font-size: 13px;
        line-height: 23px;
      }
      .img {
        width: 78px;
        margin: 8px auto;
      }
    }
    .myInstruction {
      margin: 12px auto;
      background-color: white;
      border-radius: 5px;
      border: 1px solid #ffffff;
      width: 96%;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      padding: 12px;
      box-shadow: 0px 2px 5px 0px #0000000c;
      .topDesc {
        .ltBg {
          // background: url('../img/myinstr.png') center/100% 100% no-repeat;
          width: 167px;
          height: 81px;
          // border-radius: 50px;
          // background: linear-gradient(-90deg, #e0f1fe00 0%, #d4ecfe 100%);
          color: #0558e1;
          font-family: Tensentype ZhiHeiJ-W3;
          font-weight: regular;
          font-size: 15px;
          line-height: normal;
          letter-spacing: 0px;
          text-align: left;
          display: flex;
          align-items: center;
          justify-content: center;
          margin: -40px 0 0 -15px;
          span {
            transform: translate(12px, 9px);
          }
        }
        .time {
          color: #a3a3a3;
          font-family: PingFang SC;
          font-weight: medium;
          font-size: 13px;
          line-height: normal;
          letter-spacing: 0px;
          text-align: left;
          transform: translate(0px, -7px);
        }
      }
      .text {
        color: #212121;
        font-family: PingFang SC;
        font-weight: medium;
        font-size: 14px;
        line-height: 22px;
        letter-spacing: 0px;
        text-align: left;
        //   display: -webkit-box;
        //   -webkit-box-orient: vertical;
        //   -webkit-line-clamp: ;
        //   overflow: hidden;
        //   text-overflow: ellipsis;
      }
    }
    .common_box {
      margin: 12px auto;
      background-color: white;
      border-radius: 5px;
      border: 1px solid #ffffff;
      width: 96%;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      padding: 12px;
      box-shadow: 0px 2px 5px 0px #0000000c;
      .topDesc {
        display: flex;
        justify-content: space-between;
        align-items: center;
        .title {
          color: #212121;
          font-family: PingFang SC;
          font-weight: bold;
          font-size: 16px;
          line-height: normal;
          letter-spacing: 0px;
          text-align: left;
          span {
            vertical-align: middle;
            padding: 1px 4px;
            border-radius: 2px;
            background: #0290f919;
            color: #31da8d;
            font-family: PingFang SC;
            font-weight: bold;
            font-size: 12px;
            line-height: 12px;
            letter-spacing: 0px;
            text-align: left;
            margin-left: 5px;
          }
        }
        .title::before {
          content: '';
          display: inline-block;
          width: 8px;
          vertical-align: middle;
          margin-right: 5px;
          height: 17px;
          // background: url('../img/title_before.png') center/100% 100% no-repeat;
        }
        .time {
          color: #a3a3a3;
          font-family: PingFang SC;
          font-weight: medium;
          font-size: 13px;
          line-height: normal;
          letter-spacing: 0px;
          text-align: left;
        }
      }
      .text {
        margin-top: 8px;
        color: #212121;
        font-family: PingFang SC;
        font-weight: medium;
        font-size: 14px;
        line-height: 22px;
        letter-spacing: 0px;
        text-align: left;
      }
    }
  }
}
</style>
