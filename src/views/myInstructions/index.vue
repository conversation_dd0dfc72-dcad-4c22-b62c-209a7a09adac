<template>
  <div class="instructionBox">
    <div class="iconlist">
      <div
        v-for="(item, i) in instructionData"
        :key="i"
        :class="{ iconbox: true, active: active === item.id }"
        @click="tabClick(item.id)"
      >
        <img :src="item.img" alt="" />
        <div class="name">{{ item.name }}</div>
        <!-- <div class="num">{{ item.num }}</div> -->
      </div>
    </div>
    <van-loading v-if="loading" size="25px" color="white" text-color="white" type="spinner" vertical class="loading">
      加载中...
    </van-loading>
    <div v-if="instructionList && instructionList.length > 0" class="instructionList">
      <div v-for="(item, i) in instructionList" :key="i" class="contItem" @click="showDetail(item)">
        <div class="top">
          <div class="topicName" :style="{ color: currentColor }">
            {{ item.workOrderTitle }}
          </div>
          <div class="time">{{ item.createTime }}批示</div>
        </div>
        <div class="cont">{{ item.workOrderContent }}</div>
      </div>
    </div>
    <div v-else>
      <van-empty image="search" class="empty" description="暂无数据..." />
    </div>
  </div>
</template>

<script>
import img1 from '../home/<USER>/yipishi.png'
import img2 from '../home/<USER>/tuijin.png'
import img3 from '../home/<USER>/banjie.png'
import { getAction, postAction } from '@/api/manage'

export default {
  data() {
    return {
      active: 1,
      currentColor: '',
      colorArray: ['#0c93fa', '#31da8d', '#ffa029'],
      userInfo: null,
      loading: false,
      instructionList: null,
      instructionData: [
        {
          id: 1,
          img: img1,
          name: '已批示'
        },
        {
          id: 2,
          img: img2,
          name: '推进中'
        },
        {
          id: 3,
          img: img3,
          name: '已办结'
        }
      ]
    }
  },
  mounted() {
    this.currentColor = this.colorArray[0]
    document.title = '我的批示'
    this.userInfo = JSON.parse(localStorage.getItem('userInfo')).value
    this.loadData()
  },
  methods: {
    showDetail(item) {
      this.$router.push({
        path: '/instructionsDetail',
        query: { id: item.id, topicName: item.workOrderTitle }
      })
    },
    tabClick(id) {
      if (this.active === id) {
        return
      }
      this.loading = true
      this.active = id
      this.loadData()
    },
    loadData() {
      getAction(`/cockpit/workorder/scCockpitWorkOrder/listByUserId`, {
        // useId: this.userInfo.id,
        // type: this.active,
        pageSize: 1000,
        pageNo: 1,
        status: this.active
      }).then((res) => {
        this.loading = false
        this.instructionList = res.result.records
        this.currentColor = this.colorArray[this.active - 1]
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.instructionBox {
  width: 100vw;
  height: 100vh;
  background: #f4f8fb;
  display: flex;
  flex-direction: column;
  .iconlist {
    background-color: white;
    display: flex;
    align-items: center;
    justify-content: space-evenly;
    height: 260px;

    .iconbox {
      height: 100%;
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      border-bottom: 5px solid transparent;

      img {
        width: 86px;
        height: 86px;
      }
      .name {
        color: #212121;
        font-family: PingFang SC;
        font-weight: bold;
        font-size: 32px;
        line-height: normal;
        letter-spacing: 0px;
        text-align: left;
        margin: 8px 0 0;
      }
      .num {
        color: #212121;
        font-family: DIN;
        font-weight: bold;
        font-size: 48px;
        line-height: normal;
        letter-spacing: 0px;
        text-align: left;
      }
    }
    .active {
      border-bottom: 5px solid #0290f9;
    }
  }
  .loading {
    position: absolute;
    top: 50%;
    left: 50%;
    z-index: 100;
    transform: translate(-50%, -50%);
    width: 270px;
    height: 240px;
    border-radius: 25px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(0, 0, 0, 0.7);
  }
  .instructionList {
    margin-top: 25px;
    flex: 1;
    overflow-y: auto;
    .contItem {
      margin: 0px auto 25px;
      background-color: white;
      border-radius: 10px;
      border: 2px solid #ffffff;
      width: 702px;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      padding: 24px;
      box-shadow: 0px 4px 10px 0px #0000000c;
      .top {
        display: flex;
        justify-content: space-between;
        align-items: center;
        .topicName {
          padding: 4px 8px;
          border-radius: 5px;
          background: #0290f919;
          color: #0290f9;
          font-family: PingFang SC;
          font-weight: bold;
          font-size: 26px;
          line-height: normal;
          letter-spacing: 0px;
          text-align: left;
        }
        .time {
          color: #a3a3a3;
          font-family: PingFang SC;
          font-weight: medium;
          font-size: 26px;
          line-height: normal;
          letter-spacing: 0px;
          text-align: left;
        }
      }
      .cont {
        margin-top: 16px;
        color: #212121;
        font-family: PingFang SC;
        font-weight: medium;
        font-size: 28px;
        line-height: 44px;
        letter-spacing: 0px;
        text-align: left;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }
  .empty {
    position: absolute;
    padding: 0;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    ::v-deep .van-empty__description {
      padding: 0;
    }
  }
}
@media (min-width: 345px) {
  .instructionBox {
    .iconlist {
      height: 130px;

      .iconbox {
        border-bottom: 2px solid transparent;

        img {
          width: 43px;
          height: 43px;
        }
        .name {
          font-size: 16px;
          margin: 4px 0 0;
        }
        .num {
          font-size: 24px;
        }
      }
      .active {
        border-bottom: 2px solid #0290f9;
      }
    }
    .loading {
      position: absolute;
      top: 50%;
      left: 50%;
      z-index: 100;
      transform: translate(-50%, -50%);
      width: 135px;
      height: 120px;
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      background: rgba(0, 0, 0, 0.8);
      .van-loading__text {
        font-size: 14px;
      }
    }
    .instructionList {
      margin-top: 12px;

      .contItem {
        margin: 0px auto 12px;
        background-color: white;
        border-radius: 5px;
        border: 1px solid #ffffff;
        width: 96%;
        padding: 12px;
        box-shadow: 0px 2px 5px 0px #0000000c;
        .top {
          .topicName {
            padding: 2px 4px;
            border-radius: 2px;
            font-size: 13px;
          }
          .time {
            font-size: 13px;
          }
        }
        .cont {
          margin-top: 8px;
          font-size: 14px;
          line-height: 22px;
        }
      }
    }
  }
}
</style>
