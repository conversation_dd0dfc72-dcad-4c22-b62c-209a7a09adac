<template>
  <van-pull-refresh v-model="isLoading" :disabled="disabled" @refresh="onRefresh">
    <div class="container index-container">
      <div class="top">
        <banner
          :icon-all-list="iconAllList"
          :function-list="functionList"
          @provincalConditionClick="provincalConditionClick"
        >
        </banner>
        <iconList :icon-all-list="collectIconListHomePage" :see-more-icon="seeMoreIcon" @gotoTopic="gotoTopic">
          <div class="bannerTopicFlex">
            <div class="bannerTopic" @click="gotoTopic(collectIconList.find((item) => item.topicName === '山东省情'))">
              <img src="@/assets/img/山东省情.png" alt="" />
              <div class="title">山东省情</div>
            </div>
            <div class="bannerTopic" @click="gotoTopic({ topicName: '参考手册' })">
              <img src="@/assets/img/manual.png" alt="" />
              <div class="title">参考手册</div>
            </div>
            <div class="bannerTopic" @click="gotoTopic({ topicName: '运行状况' })">
              <img src="@/assets/img/运行状况.png" alt="" />
              <div class="title">运行状况</div>
            </div>
            <div class="bannerTopic" @click="gotoTopic({ topicName: '我的批示' })">
              <img src="@/assets/img/我的批示.png" alt="" />
              <div class="title">我的批示</div>
            </div>
            <div class="bannerTopic" @click="gotoTopic({ topicName: '专题生成' })">
              <img src="@/assets/img/专题生成.png" alt="" />
              <div class="title">专题生成</div>
            </div>
          </div>
        </iconList>
      </div>
      <div class="topic-page-index">
        <jjyx />
        <whly />
        <nyqk />
        <jysy />
        <czqk />
        <fdc />
        <xfqk />
        <spjg />
        <myjj />
      </div>
    </div>
    <popup ref="popup" @closeHandler="closeHandler" />
    <topicGeneratePopup ref="topicGeneratePopup" @closeHandler="closeHandler" />

    <!-- <userRegistration ref="userRegistration" @closeHandler="closeHandler" /> -->
  </van-pull-refresh>
</template>

<script>
import Vue from 'vue'
import { getHomeListApi, getTopicListApi } from '@/api/homepage'
import jjyx from './components/EcnomicOperation.vue'
import whly from './components/CultureTourism.vue'
import nyqk from './components/LowCarbonEnergy.vue'
import jysy from './components/ResidentEmployment.vue'
import czqk from './components/FiscalExpenditure.vue'
import fdc from './components/RealEstateOnlineSigning.vue'
import xfqk from './components/xfqk.vue'
import spjg from './components/spjg.vue'
import myjj from './components/myjj.vue'
import popup from './components/popup.vue'
import topicGeneratePopup from './components/topicGeneratePopup.vue'
// import userRegistration from './components/userRegistration.vue'
import banner from './components/banner.vue'
import { getAction, getActionWithToken2 } from '@/api/manage'
import iconList from './components/iconList.vue'
import { axios } from '@/utils/request'
import { routeOfNotFound } from '@/router'
import { homepageTopicList } from './topicConstant'
import { mapWritableState } from 'pinia'
import { topicDataStore } from '@/stores/component'

import '@/utils/turnjs/lib/turn.js'
import { pick } from 'lodash'
export default {
  name: 'Home',
  components: {
    jjyx,
    whly,
    xfqk,
    spjg,
    myjj,
    nyqk,
    jysy,
    czqk,
    fdc,
    banner,
    iconList,
    popup,
    topicGeneratePopup
    // userRegistration,
  },
  data() {
    return {
      homepageTopicList,
      AuthToken: null,
      disabled: false,
      isLoading: false,
      showTopicAndManual: false,
      userInfo: null,
      iconAllList: [],
      collectIconList: [],
      seeMoreIcon: null, //查看更多右上角的图标显示与否
      // websock: null,
      queryTimer: null, // 保存计时器的引用
      functionList: [],
      pageCav: [],
      currentPage: 1
    }
  },
  computed: {
    collectIconListHomePage() {
      return this.collectIconList.filter((item) =>
        this.isPad
          ? homepageTopicList.homePage2.includes(item.topicName)
          : homepageTopicList.homePage1.includes(item.topicName)
      )
    },
    ...mapWritableState(topicDataStore, ['isPad'])
  },
  beforeDestroy() {
    // 清除定时器
    if (this.queryTimer) {
      clearInterval(this.queryTimer)
    }
    window.removeEventListener('resize', () => {
      this.isPad = window.matchMedia('(min-width: 600px)').matches
    })
  },
  async mounted() {
    await this.init()
    this.isPad = window.matchMedia('(min-width: 600px)').matches
    window.addEventListener('resize', () => {
      this.isPad = window.matchMedia('(min-width: 600px)').matches
    })
  },
  methods: {
    async init() {
      document.title = '经济运行分析预测平台'
      this.userInfo = JSON.parse(localStorage.getItem('userInfo')).value
      this.AuthToken = JSON.parse(localStorage.getItem('AuthToken')).value
      // await this.getusergrouplist()
      // this.getAuthValue()
      // this.getAuthTopic()
      Vue.ls.set('HIDE_COLLECT', true) //是否展示收藏按钮，目前城市通与领导端公用一套服务，所以需要前端区分，只有城市通打开的需要隐藏，其余都展示
      // this.queryUnReadMsg()
      if (!sessionStorage.getItem('dynamicRoutesMenuData')) await this.getHomeList()
      else this.collectIconList = JSON.parse(sessionStorage.getItem('dynamicRoutesMenuData'))
      // await this.getAllList(this.userInfo.id)
    },
    getusergrouplist() {
      getActionWithToken2(
        import.meta.env.VITE_APP_SITUATION_BASE_URL + `app/appIndexTopic/getUserTopicGroup?userId=${this.userInfo.id}`,
        {},
        this.AuthToken
      ).then((res) => {
        if (res.result === null) {
          this.$router.push('/noAuthority')
          return
        }
      })
    },
    getAuthTopic() {
      const phone = JSON.parse(localStorage.getItem('userInfo'))?.value.phone
      getAction(`${import.meta.env.VITE_APP_SITUATION_BASE_URL}topic/data/listAllBySql/ydd_homepage_hide`, {
        phone
      }).then((res) => {
        if (res.result.length > 0) {
          this.showTopicAndManual = false
        } else {
          this.showTopicAndManual = true
        }
      })
    },
    queryUnReadMsg() {
      //启动计时器查询是否有未读消息
      if (this.queryTimer) {
        clearInterval(this.queryTimer)
      }
      this.queryTimer = setInterval(async () => {
        await this.getHomeList()
        // await this.getAllList(this.userInfo.id)
      }, 5000)
    },
    async getAuthValue() {
      //获取控制
      const userGuid = this.userInfo.id
      const authRes = await getAction(
        `${import.meta.env.VITE_APP_SITUATION_BASE_URL}/app/appFunction/queryUserFunctionList?userGuid=${userGuid}`
      )
      this.funcObj = authRes.result
    },
    getAllList(userGuid) {
      return getHomeListApi({ userGuid }).then((res) => {
        if (res.success) {
          this.iconAllList = res.result
          localStorage.setItem('iconAllList', JSON.stringify(this.iconAllList))
        }
      })
    },
    async getHomeList() {
      //获取首页收藏的图标列表

      return getTopicListApi().then((res) => {
        if (res.success) {
          this.seeMoreIcon = res.result.other == 1 ? true : false
          this.collectIconList = res.result?.menuList || []
          this.topicBasicInfoList = this.collectIconList
          this.collectIconList.push({ topicName: '查看更多' })
          // 将菜单列表存入sessionStorage，以便路由守卫使用
          if (res.result?.menuList) {
            sessionStorage.setItem(
              'dynamicRoutesMenuData',
              JSON.stringify(
                res.result.menuList.map((menu) => {
                  return pick(
                    menu,
                    'topicUrl',
                    'topicId',
                    'topicCode',
                    'linkType',
                    'topicCode',
                    'topicIcon',
                    'topicName'
                  )
                })
              )
            )
          }
          const existingRoutes = this.$router.getRoutes()
          res.result?.menuList?.forEach((item) => {
            if (!existingRoutes.some((route) => route.name === item.topicId)) {
              this.$router.addRoute({
                path: `/${item.topicCode}`,
                name: item.topicId,
                component: () => import(`@/views/${item.topicCode}/index.vue`),
                meta: {
                  title: item.topicName
                }
              })
            }
          })

          // 添加404路由
          if (!existingRoutes.some((route) => route.name === 'notFound')) {
            this.$router.addRoute(routeOfNotFound)
          }

          // this.$store.dispatch('actionUpDateIconList', this.collectIconList)
        } else if (res.code == 510 && res.message.includes('Token失效')) {
          this.$toast(res.message)
          this.$router.push('/login')
        }
      })
    },
    provincalConditionClick(e) {
      //点击山东省情时候
      this.gotoTopic(e)
    },
    addLog(topicName) {
      //添加用户的访问记录
      getAction(`${import.meta.env.VITE_APP_SITUATION_BASE_URL}home/addLog`, { topicName })
    },
    onRefresh() {
      //下拉刷新
      location.reload()
    },
    closeHandler() {
      this.disabled = false
    },
    handleJump(topicName) {
      const topic = this.iconAllList.find((item) => item.topicName === topicName)
      this.gotoTopic(topic, 'dontCancleUnReadMsg')
    },
    deleteUnReadMsg(topicId, userGuid) {
      return axios({
        url: `${import.meta.env.VITE_APP_SITUATION_BASE_URL}sysUserUnreadMsg/deleteByUserGuidAndTopicId`,
        method: 'delete',
        params: { topicId, userGuid }
      })
    },
    processUnicode(input) {
      return encodeURIComponent(input)
    },
    processNumbers(input) {
      return btoa(input)
    },
    // 专题详情
    async gotoTopic(topic) {
      // if (topic === '查看更多') {
      //   this.$router.push('/seeMore')
      //   return
      // }
      if (topic.topicName === '参考手册') this.$router.push('referenceManual')
      else if (topic.topicName === '运行状况') {
        this.$refs.popup.show = true
      } else if (topic.topicName === '我的批示') this.$router.push('/myInstructions')
      else if (topic.topicName === '查看更多') this.$router.push('/seeMore')
      else if (topic.topicName === '专题生成') {
        this.$refs.topicGeneratePopup.show = true
      }
      const { topicUrl, topicId, topicCode, linkType, topicName } = topic
      //进入除首页外其他专题的时候调接口,取消首页未读消息的徽标
      if (linkType === 0) {
        this.$router.push({
          path: `/${topicCode}`,
          params: topic,
          query: {
            id: topicId,
            name: topicName
          }
        })
      } else if (linkType == 1) {
        // var url = `/${routeMap[name]}?id=${id}&name=${name}&hasUnreadMsg=${hasUnreadMsg}&_t=${Date.now()}`
        // location.href = url
        // return
        this.$router.push({
          path: topicUrl,
          params: topic
        })
      } else if (linkType == 2) {
        //   //外链跳转的时候
        //   // this.addLog(name)
      }
      // } else {
      //   //topic页面跳转的时候
      //   goTopicPageNew(name, id, subCatalogType, hasUnreadMsg)
      // }
    }
  }
}
</script>
<style lang="scss" scoped>
.container {
  width: 100%;
  min-height: 100vh;
  background-color: #abccff;

  .top {
    padding-top: 0;
    width: 100%;
    min-height: auto;
    position: relative;
  }

  .hideStyle {
    height: 100vh !important;
  }
}

.topic-page-index {
  margin-top: 0;
  margin-left: auto;
  .addMargin {
    margin-bottom: 20px;
  }
}

//下拉刷新文字修改
::v-deep .van-loading__text {
  font-size: 28px;
}
::v-deep .van-pull-refresh__head {
  line-height: 160px;
}
.bannerTopicFlex {
  position: absolute;
  height: 130px;
  top: -130px;
  display: flex;
  justify-content: start;
  align-items: center;
  width: 100%;
}
.bannerTopic {
  height: 100%;
  width: 150px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  img {
    width: 50px;
    height: 50px;
    margin-left: revert;
  }
  &:nth-of-type(1) {
    img {
      width: 75px;
    }
  }
  .title {
    padding-left: 0;
    font-family: SourceHanSansCN, SourceHanSansCN;
    font-weight: 400;
    font-size: 24px;
    color: #fff;
    line-height: 42px;
    text-align: center;
    font-style: normal;
  }
}
@media screen and (min-width: 600px) {
  .bannerTopicFlex {
    top: -90px;
    height: 100px;
  }
  .bannerTopic {
    width: 100px;

    img {
      width: 36px;
      height: 36px;
    }
    &:nth-of-type(1) {
      img {
        width: 54px;
      }
    }
    .title {
      font-size: 14px;
      line-height: 27px;
    }
  }
}
</style>
