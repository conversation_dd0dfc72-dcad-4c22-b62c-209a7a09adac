import Vue from 'vue'
import { create<PERSON><PERSON>, PiniaVuePlugin } from 'pinia'
import Storage from 'vue-ls'
import VConsole from 'vconsole'
import App from './App.vue'
import 'animate.css'
import router from './router'
import './utils/lib/vant'
import './utils/lib/elementUI'
import echarts from './utils/lib/echarts'
import VueDOMPurifyHTML from 'vue-dompurify-html'
import store from './store/index'
import populationPopup from './components/populationPopup.vue'
import areaPopup from './components/areaPopup.vue'
import { getDataSourceByTopicCode } from './api/getDataSourceByTopicCode'
import PopupOfLargeModels from './components/globalComponent/PopupOfLargeModels.vue'
import writtenInstructions from './components/globalComponent/writtenInstructions.vue'
import screenShot from './components/globalComponent/ScreenShot.vue'
import hasUpdataVue from './components/hasUpdata.vue'
import hasUpdataVue2 from './components/hasUpdata2.vue'
import commonEchartDom from '@/components/componentsTemplate/commonEchartDom.vue'
import '@/components/componentsTemplate/register-cpt.js'
import vueBus from '@/utils/vueBus'
import { v4 as uuidv4 } from 'uuid'
import { convertPxToVw } from './utils/util'
import { getStyle } from './utils/util'
import { getWhere } from './utils/util'
// 放在最后，防止样式覆盖
import './style/basic.scss'
import VueMarkdown from 'vue-markdown'
import commonPageContent from './views/commonPageContent.vue'
import watermark from './directives/watermark'
import { emitHeightDiff } from './utils/util'
import fetchEventSourceMixin from '@/mixins/fetchEventSourceMixin.js'
Vue.config.productionTip = false
Vue.use(Storage, {
  namespace: 'yt__', // key键前缀
  name: 'ls', // 命名Vue变量.[ls]或this.[$ls],
  storage: 'local' // 存储名称: session, local, memory
})
Vue.use(PiniaVuePlugin)
Vue.use(VueDOMPurifyHTML)
Vue.use(vueBus)
Vue.component('PopupOfLargeModels', PopupOfLargeModels)
Vue.component('WrittenInstructions', writtenInstructions)
Vue.component('PopulationPopup', populationPopup)
Vue.component('ScreenShot', screenShot)
Vue.component('AreaPopup', areaPopup)
Vue.component('VueMarkdown', VueMarkdown)
Vue.component('CommonEchartDom', commonEchartDom)
// Vue.use(Popup)
Vue.component('PopulationPopup', populationPopup)
Vue.component('AreaPopup', areaPopup)
Vue.component('HasUpdata', hasUpdataVue)
Vue.component('HasUpdata2', hasUpdataVue2)
Vue.component('CommonPageContent', commonPageContent)
Vue.directive('watermark', watermark)
Vue.prototype.$echarts = echarts
Vue.prototype.$uuid = uuidv4
Vue.prototype.$convertPxToVw = convertPxToVw
Vue.prototype.$getStyle = getStyle
Vue.prototype.$getWhere = getWhere
Vue.prototype.$emitHeightDiff = emitHeightDiff
const baseUrl = import.meta.env.VITE_APP_SITUATION_BASE_URL
const mode = import.meta.env.MODE
Vue.prototype.$showLargeModel = baseUrl.includes('/situation_dev/') || mode == 'development'
// Vue.prototype.$showLargeModel = false
Vue.prototype.$eventBus = new Vue()

Vue.prototype.$getDataSourceByTopicCode = getDataSourceByTopicCode
Vue.mixin(fetchEventSourceMixin)
const pinia = createPinia()
if (import.meta.env.NODE_ENV == 'production' && import.meta.env.VITE_APP_VCONSOLE == 'true') {
  new VConsole()
}

new Vue({
  router,
  store,
  pinia,
  render: (h) => h(App)
}).$mount('#app')
