<template>
  <div v-if="showSreenShot" class="screenShotMask">
    <van-popup v-model="showVanPopup" :overlay="false">{{ apiMsg }}</van-popup>
    <van-loading
      v-if="loading"
      :color="loading_color"
      :text-color="loading_color"
      size="25px"
      type="spinner"
      vertical
      class="loading"
    >
      加载中...
    </van-loading>
    <div class="screenShot_submitBtn">
      <van-button type="warning" class="warning" @click="close">取消</van-button>
      <van-button type="info" class="info" @click="confirm">确定</van-button>
    </div>
    <div
      v-if="unSubmit"
      class="confirmCont"
      :style="{
        width: screenParams.width - padding + 'px',
        height: screenParams.height - padding + 'px'
      }"
    >
      <div class="confirm_topCont">
        <div class="imgbox">
          <img :src="imgUrl" alt="" />
        </div>
        <van-cell-group class="input">
          <van-field
            v-model="message"
            type="textarea"
            label="批示"
            rows="4"
            placeholder="请输入批示内容"
            required
            :clearable="false"
          />
        </van-cell-group>
        <div class="confirm__btn">
          <van-button type="warning" class="warning" @click="close">取消</van-button>
          <van-button type="info" class="info" @click="submit">提交</van-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import html2canvas from 'html2canvas'
import { getAction, postAction } from '@/api/manage'

export default {
  data() {
    return {
      showVanPopup: false, //展示弹窗
      apiMsg: '', //接口返回的内容
      message: '', //批示内容
      unSubmit: false, //未点击提交前的批示页面
      imgUrl: '',
      loading_color: 'white',
      loading: false,
      canvas: null,
      showSreenShot: false,
      context: null, // Canvas上下文
      isDrawing: false, // 涂鸦状态
      lastX: 0,
      padding: 40,
      screenParams: {},
      lastY: 0
    }
  },
  watch: {
    showSreenShot(newvalue) {
      if (newvalue === true) {
        this.init()
        this.showSreenShot = true
        this.loading = true
        this.captureScreenshot()
        document.body.classList.add('modal-open')
      } else {
        document.body.classList.remove('modal-open')
      }
    }
  },
  mounted() {
    this.screenParams = {
      width: window.innerWidth,
      height: window.innerHeight
    }
  },
  methods: {
    removeSreen() {
      let el = document.querySelector('.screenShot_maskDiv')
      let btn = document.querySelector('.screenShot_submitBtn')
      if (btn) {
        btn.style.visibility = 'hidden'
      }
      if (el) {
        el.remove()
      }
    },
    init() {
      this.unSubmit = false
      this.context = null
    },
    close() {
      this.showSreenShot = false
      this.message = ''
      this.imgUrl = ''
      this.removeSreen()
    },
    captureScreenshot() {
      let that = this
      const scrollX = window.scrollX
      const scrollY = window.scrollY
      html2canvas(document.body, {
        width: this.screenParams.width,
        height: this.screenParams.height,
        x: scrollX,
        y: scrollY
      }).then((el) => {
        that.loading = false
        that.canvas = el
        that.context = that.canvas.getContext('2d')
        let page = document.querySelector('.screenShotMask')
        let newDiv = document.createElement('div')
        let btn = document.querySelector('.screenShot_submitBtn')
        newDiv.classList.add('screenShot_maskDiv')
        page.appendChild(newDiv)
        console.log(that.canvas)
        that.canvas.style.width = '100%'
        that.canvas.style.height = '100%'
        that.canvas.addEventListener('touchstart', that.startDrawing, false)
        that.canvas.addEventListener('touchmove', that.draw, false)
        that.canvas.addEventListener('touchend', that.stopDrawing, false)
        newDiv.appendChild(that.canvas)
        btn.style.visibility = 'visible'
        newDiv.appendChild(btn)
      })
    },

    // 开始涂鸦
    startDrawing(e) {
      this.isDrawing = true
      this.lastX = e.offsetX || e.touches[0].clientX + window.scrollX
      this.lastY = e.offsetY || e.touches[0].clientY + window.scrollY
    },

    // 绘制涂鸦
    draw(e) {
      const currentX = e.offsetX || e.touches[0].clientX + window.scrollX
      const currentY = e.offsetY || e.touches[0].clientY + window.scrollY

      this.context.beginPath() //开始一条新的路径。
      this.context.moveTo(this.lastX, this.lastY) //将画笔移动到上一次的坐标点。
      this.context.lineTo(currentX, currentY) //从上一个点画一条线到当前点。
      this.context.strokeStyle = 'red' // 设置线条颜色
      this.context.lineWidth = 2 // 设置线条宽度
      this.context.stroke() //绘制线条。
      this.lastX = currentX
      this.lastY = currentY
    },

    // 停止涂鸦
    stopDrawing() {
      this.isDrawing = false
    },

    confirm() {
      this.imgUrl = this.canvas.toDataURL('image/png')
      this.removeSreen()
      this.loading = true
      setTimeout(() => {
        this.unSubmit = true
        this.loading = false
      }, 800)
    },
    submit() {
      this.loading = true
      const urlParams = new URLSearchParams(window.location.search)
      // const userInfo = JSON.parse(localStorage.getItem('userInfo')).value
      postAction('/cockpit/workorder/scCockpitWorkOrder/add', {
        workOrderFile: this.imgUrl,
        // domainUrl: location.href,
        workOrderTitle: urlParams.get('name'),
        workOrderContent: this.message,
        // domain: urlParams.get('name'),
        // userPhone: userInfo.phone,
        // userName: userInfo.username,
        source: 'lc',
        platform: 'mobile'
      })
        .then((res) => {
          this.showVanPopup = true
          this.apiMsg = res.message
          setTimeout(() => {
            this.showVanPopup = false
            this.close()
          }, 1000)
        })
        .catch((err) => {
          this.showVanPopup = true
          this.apiMsg = err
          setTimeout(() => {
            this.showVanPopup = false
          }, 1000)
        })
        .finally(() => {
          this.loading = false
        })
    }
  }
}
</script>

<style lang="scss" scoped>
.screenShotMask {
  background: rgba(0, 0, 0, 0.8);
  position: fixed;
  width: 100%;
  height: 100%;
  z-index: 100;
  .van-popup {
    font-size: 32px;
    padding: 20px 40px;
    border-radius: 25px;
    display: flex;
    color: white;
    align-items: center;
    justify-content: center;
    background: rgba(0, 0, 0, 0.7);
  }
  .loading {
    position: absolute;
    top: 50%;
    left: 50%;
    z-index: 100;
    transform: translate(-50%, -50%);
    width: 270px;
    height: 240px;
    border-radius: 25px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(0, 0, 0, 0.3);
  }

  .confirmCont {
    position: relative;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    .confirm_topCont {
      height: 100%;
      padding: 40px 20px;
      background: white;
      border-radius: 15px;
      overflow: hidden;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: space-between;
      .imgbox {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 200px;
        overflow-y: auto;
        img {
          width: 90%;
        }
      }

      .input {
        width: 100%;
        .van-cell {
          flex-direction: column;
        }
      }
    }
    .confirm__btn {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;
      .warning,
      .info {
        flex: 0.45;
        height: 80px;
        padding: 0;
        margin-top: 35px;
      }
      .warning {
        background-color: rgb(234, 245, 255);
        color: black;
        border: 1px solid rgb(220, 222, 224);
      }
    }
  }
}
@media (min-width: 345px) {
  .screenShotMask {
    .van-popup {
      font-size: 14px;
      padding: 10px 20px;
      border-radius: 12px;
      display: flex;
      color: white;
      align-items: center;
      justify-content: center;
      background: rgba(0, 0, 0, 0.7);
    }
    .loading {
      position: absolute;
      top: 50%;
      left: 50%;
      z-index: 100;
      transform: translate(-50%, -50%);
      width: 135px;
      height: 120px;
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      background: rgba(0, 0, 0, 0.8);
      .van-loading__text {
        font-size: 14px;
      }
    }

    .confirmCont {
      position: relative;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      .confirm_topCont {
        height: 100%;
        padding: 20px 10px;
        background: white;
        border-radius: 7px;
        overflow: hidden;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: space-between;
        .imgbox {
          flex: 1;
          display: flex;
          align-items: center;
          justify-content: center;
          width: 100%;
          height: 100px;
          overflow-y: auto;
          img {
            width: 90%;
          }
        }

        .input {
          width: 100%;
          .van-cell {
            padding: 15px 20px;
            font-size: 14px;
            line-height: 22px;
            flex-direction: column;
          }
          .van-cell--required::before {
            font-size: 22px;
            left: 5px;
          }
        }
      }
      .confirm__btn {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
        .warning,
        .info {
          font-size: 12px;
          flex: 0.45;
          height: 40px;
          padding: 0;
          margin-top: 17px;
        }
        .warning {
          background-color: rgb(234, 245, 255);
          color: black;
          border: 1px solid rgb(220, 222, 224);
        }
      }
    }
  }
}
</style>
<style>
body.modal-open {
  overflow: hidden;
}
</style>
<style lang="scss">
.screenShot_submitBtn {
  visibility: hidden;
  display: flex;
  align-items: center;
  justify-content: space-evenly;
  position: absolute;
  bottom: 60px;
  width: 480px;
  height: 100px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 999;
  background-color: rgba(0, 0, 0, 0.7);
  .warning,
  .info {
    width: 160px;
    height: 50px;
    padding: 0;
  }
  .warning {
    background-color: rgb(234, 245, 255);
    color: black;
    border: 1px solid rgb(234, 245, 255);
  }
}
@media (min-width: 345px) {
  .screenShot_submitBtn {
    display: flex;
    align-items: center;
    justify-content: space-evenly;
    position: absolute;
    bottom: 60px;
    width: 240px;
    height: 50px;
    background-color: rgba(0, 0, 0, 0.7);
    .warning,
    .info {
      font-size: 12px;
      width: 80px;
      height: 25px;
      padding: 0;
    }
    .warning {
      background-color: rgb(234, 245, 255);
      color: black;
      border: 1px solid rgb(234, 245, 255);
    }
  }
}
.screenShot_maskDiv {
  position: absolute;
  left: 0px;
  top: 0px;
  width: 100vw;
  height: 100vh;
  z-index: 100;
  padding: 40px;
}
</style>
