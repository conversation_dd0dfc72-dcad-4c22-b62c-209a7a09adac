import Vue from 'vue'
import VueRouter from 'vue-router'
import Home from '@/views/home/<USER>'
import Login from '@/views/user/login.vue'
import { beforeEachGuard } from './guard.js'
// const originalPush = VueRouter.prototype.push
// 防止重复路由报错
// VueRouter.prototype.push = function push(location) {
//   if (typeof location === 'string') {
//     return originalPush.call(this, location + `&_t=${Date.now()}`).catch((err) => err)
//   } else {
//     location.query = { ...location.query, _t: Date.now() }
//     return originalPush.call(this, location).catch((err) => err)
//   }
// }

Vue.use(VueRouter)
const routes = [
  {
    path: '/',
    name: 'root',
    redirect: '/home'
  },
  {
    path: '/home',
    name: 'Home',
    component: Home
  },
  {
    path: '/login',
    name: 'Login',
    component: Login
  },
  {
    path: '/seeMore',
    name: 'seeMore',
    component: () => import('@/views/home/<USER>/seeMore.vue')
  },
  {
    path: '/referenceManual',
    name: 'referenceManual',
    component: () => import('@/views/referenceManual/index.vue')
  },
  {
    path: '/myInstructions',
    name: 'myInstructions',
    component: () => import('@/views/myInstructions/index.vue')
  },
  {
    path: '/instructionsDetail',
    name: 'instructionsDetail',
    component: () => import('@/views/myInstructions/instructionsDetail.vue')
  },
  {
    path: '/topicGenerate',
    name: 'topicGenerate',
    component: () => import('@/views/topicGenerate/index.vue')
  }
]

export const routeOfNotFound = {
  path: '/:pathMatch(.*)*',
  name: 'notFound',
  component: () => import('@/components/custom/notFound/index.vue')
}

const router = new VueRouter({
  mode: 'history',
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { x: 0, y: 0 }
    }
  }
})
router.beforeEach(beforeEachGuard)
export default router
