// src/mixins/fetchEventSourceMixin.js
import { fetchEventSource } from '@microsoft/fetch-event-source'
import MarkdownIt from 'markdown-it'
export default {
  data() {
    return {
      sseController: null,
      isStreaming: false,
      sseError: null
    }
  },
  methods: {
    /**
     *
     * @param {Object} cfg
     *  ├─ url          {String}  必填，请求地址
     *  ├─ method       {String}  GET / POST，默认 'GET'
     *  ├─ headers      {Object}  额外请求头
     *  ├─ body         {Object|String}  请求体
     *  ├─ onOpen       {(resp)=>void}
     *  ├─ onMessage    {(ev)=>void}
     *  ├─ onError      {(err)=>void}
     *
     * @returns {Object} { isStreaming, error, abort }
     */
    startSSE(cfg) {
      if (!cfg?.url) throw new Error('url is required')

      // 若已有旧流，先中断
      this.abortSSE()

      this.isStreaming = true
      this.sseError = null
      this.sseController = new AbortController()

      const storedToken = localStorage.getItem('AuthToken')

      const tokenHeader = storedToken ? { 'X-Access-Token': JSON.parse(storedToken).value } : {}

      const apiBaseUrl = import.meta.env.VITE_APP_SITUATION_BASE_URL || '/jeecg-boot'

      fetchEventSource(apiBaseUrl + cfg.url, {
        openWhenHidden: true,
        method: cfg.method || 'GET',
        headers: {
          'Content-Type': 'application/json',
          ...tokenHeader,
          ...(cfg.headers || {})
        },
        body: cfg.body ? JSON.stringify(cfg.body) : undefined,
        signal: this.sseController.signal,

        onopen: cfg.onOpen,
        onmessage: cfg.onMessage,
        onerror: (err) => {
          this.sseError = err
          cfg.onError?.(err)
        },
        onclose: () => {
          this.isStreaming = false
          cfg.onClose()
        }
      }).catch((err) => {
        if (err.name !== 'AbortError') {
          this.sseError = err
          this.isStreaming = false
        }
      })
    },

    /** 手动终止 */
    abortSSE() {
      if (this.sseController) {
        this.sseController.abort()
        this.sseController = null
        this.isStreaming = false
      }
    },
    generateAi() {
      this.showDialog = true
      this.analysisMsg = '分析结果生成中...'
      this.md = new MarkdownIt()
      const cfg = {
        url: '/llm/api/analysisIndex',
        method: 'POST',
        body: {
          htmlStr: this.domStr,
          moduleId: 'cpt-container-draggable-eRId3Rly1Qtq'
        },

        onMessage: (event) => {
          if (event.data != 'ping') {
            const msg = JSON.parse(event.data)
            if (!msg.value) return
            const container = document.querySelector('.van-dialog__content')
            container.scrollTop = container.scrollHeight
            this.analysisMsg = this.analysisMsg === '分析结果生成中...' ? msg.value : this.analysisMsg + msg.value
          }
        },
        onOpen: () => {
          console.log('SSE连接已打开')
        },
        onError: (err) => {
          console.error('SSE连接错误:', err)
        },
        onClose: () => {
          console.log('SSE连接已关闭')
        }
      }
      this.startSSE(cfg)
    },
    generateTopic() {
      const cfg = {
        url: '/llm/api/generateTopic',
        method: 'POST',
        body: {
          userRequest: this.userRequest,
          workbenchCode: 'mobile'
        },

        onMessage: (event) => {
          if (event.data != 'ping') {
            const msg = JSON.parse(event.data)
            if (!msg.value) return
            const container = document.querySelector('.van-dialog__content')
            container.scrollTop = container.scrollHeight
            this.analysisMsg = this.analysisMsg === '分析结果生成中...' ? msg.value : this.analysisMsg + msg.value
          }
        },
        onOpen: () => {
          console.log('SSE连接已打开')
        },
        onError: (err) => {
          console.error('SSE连接错误:', err)
        },
        onClose: () => {
          console.log('SSE连接已关闭')
        }
      }
      this.startSSE(cfg)
    }
  },

  beforeDestroy() {
    this.abortSSE() // 组件销毁时自动清理
  }
}
