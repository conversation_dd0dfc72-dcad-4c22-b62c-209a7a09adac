@use '@/style/topic.scss';
@use '@/style/echarts.scss';
@use '@/style/baseCard.scss';
@use '@/style/pad.scss';

:root {
  box-sizing: border-box;
}

*,
:after,
:before {
  margin: 0;
  padding: 0;
  box-sizing: inherit;
  user-select: text;
}

html,
body {
  color: hsl(0, 0%, 20%);
  width: 100%;
  height: 100%;
}

ul,
li {
  list-style: none;
}

a {
  text-decoration: none;
}

a:hover {
  text-decoration: none;
}

img {
  max-width: 100%;
}

::-webkit-scrollbar {
  display: none;
}
.table__body__container::-webkit-scrollbar {
  display: block !important;
  width: 8px; /* 可选：自定义滚动条宽度 */
}
.table__body__container::-webkit-scrollbar-thumb {
  background-color: #aaaaaa;
  border-radius: 10px;
}
/*禁止长按页面是弹出的菜单*/
img,
a {
  -webkit-touch-callout: none;
}

// 常用自定义字体
@font-face {
  font-family: 'D-Din';
  src: url('@/static/fonts/D-DIN.otf');
}

@font-face {
  font-family: 'DIN-Regular';
  src: url('@/static/fonts/D-DIN.otf');
}

@font-face {
  font-family: 'DINPro';
  src: url('@/static/fonts/D-DIN.otf');
}

.van-info--dot {
  width: 6px;
  height: 6px;
  padding: 0;
}
.van-info {
  top: 50%;
  right: -5px;
}
.title-icon {
  width: 35px;
}
.van-tag {
  font-size: 18px;
}
.back-color {
  background-color: #f4f4f4;
}
// font
.font-32 {
  font-size: 24px;
}
.font-black {
  color: #333333;
}
.popover_addBg {
  background-color: rgb(230, 244, 255);
  font-weight: 600;
  border-radius: 8px;
}
.van-hairline--bottom::after {
  border-bottom-width: 0px;
}
// card
.card-title {
  font-size: 42px;
  color: #484848;
  font-weight: bold;
  padding-bottom: 30px;
}
.card {
  padding: 40px 42px 30px 42px;
  border-radius: 4px;
  background-color: #fff;
  color: #303133;
  transition: 0.3s;
  font-size: 30px;
  // margin-bottom: 20px;
}
.van-grid-item__text {
  font-size: 30px;
  color: #333333;
  padding-left: 30px;
  margin-left: 0px !important;
}
.flex-transverse {
  display: flex;
  align-items: center;
}
.flex-content {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
}
.flex-common {
  display: flex;
  justify-content: center;
  align-items: center;
}
.flex-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
// 去掉van-grid顶部线条
.van-hairline--top::after {
  border-top-width: 0px !important;
}
//DiaLog
.van-dialog {
  border-radius: 4px;
  width: 525px;
  height: 285px;
  background: #ffffff;
}
.van-dialog__header {
  font-size: 30px;
  color: #333333;
  padding-top: 40px;
}
.van-dialog__message {
  padding-top: 20px;
  padding-bottom: 39px;
}
.van-dialog__confirm,
.van-dialog__confirm:active {
  color: #0089ff;
}
.van-dialog__content {
  border-bottom: solid 1px #f4f4f4;
}
// 轮播tab下灰色的线
.van-tab {
  border: none;
  color: #666666;
  line-height: 10px;
}
.van-tabs__line {
  background: #1c77cb;
  bottom: 5px;
}
.van-tab--active {
  color: #000;
  font-weight: 600;
}
.van-tab__text {
  font-size: 15px;
  line-height: 20px;
}
.van-tabs__nav--line {
  padding-bottom: 0px !important;
}
.van-tabs__wrap {
  border-bottom: solid 1px #eeeeee;
}
.refresh {
  height: 94%;
  overflow: scroll;
}
// 搜索框
.van-search {
  width: 100%;
  height: 100px;
  background: #f4f4f4;
  padding: 10px 20px;

  .van-search__content {
    padding-left: 30px;
    background-color: #fff;
  }
  .van-cell {
    align-items: center;
  }
  .van-field__left-icon {
    margin-right: 20px;
  }
  .van-search__action {
    padding-left: 30px;
    color: #0089fe;
  }
}

//删除地图标志
.anchorBL {
  opacity: 0 !important;
}
/***************************轮播修改********************************/
.van-swipe__indicators {
  bottom: 0 !important;
}
.van-swipe__indicator {
  background-color: #0089fe;
}
.van-swipe__indicator--active {
  width: 50px !important;
  border-radius: 10px;
}
/****************************轮播修改*******************************/
$card-box-shadow: 0 4px 14px rgba(0, 0, 0, 0.11);
$card-border-radius: 8px;
// 指标
.indicator-card {
  position: relative;
  background: #ffffff;
  box-shadow: $card-box-shadow;
  border-radius: $card-border-radius;
  margin: 30px;
  padding: 30px;

  &__topic {
    font-size: 26px;
    color: #666666;
    margin-bottom: 14px;
  }
  &__title {
    font-size: 30px;
    color: #333333;
    margin-bottom: 20px;
  }
  &__footer {
    display: flex;
    justify-content: space-between;
    margin-top: 14px;
    font-size: 26px;
    color: #999999;
    line-height: 26px;
  }
  &__chart-icon {
    position: absolute;
    right: 30px;
    top: 30px;
    width: 60px;
    height: 60px;
  }
}
.btn {
  width: 140px;
  height: 58px;
  background: #0089fe;
  border-radius: 33px;
  font-size: 26px;
}
.btn-area {
  display: flex;
  justify-content: center;
  align-items: center;
}
.btn-box {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-right: 30px;
}
.under-search-container {
  margin-top: 30px;
}
.back-top {
  position: fixed;

  &__button {
    width: 75px;
    height: 75px;
    border-radius: 50%;
  }

  .van-icon {
    font-size: 45px;
  }
}
// 关闭按钮点击范围
.van-field__clear {
  position: relative;
  padding: 0 30px;

  &::after {
    content: '';
    position: absolute;
    left: -8px;
    top: -30px;
    width: 100px;
    height: 100px;
  }
}
// 隐藏上拉加载状态
.van-list__loading {
  display: none;
}

//tab切换
.query-buttons {
  margin-bottom: 20px;
  display: flex;
  justify-content: space-evenly;
  align-items: center;
  flex-flow: row wrap;

  > div {
    margin: 8px;
    padding: 10px 40px;
    // min-width: 190px;
    height: 60px;
    background: #f5f5f5;
    border-radius: 40px;
    font-size: 30px;
    line-height: 42px;
    color: #333333;
    border: none;

    &.active {
      background: #3b93fb;
      color: #ffffff;
    }
  }

  .query-button {
    margin: 8px;
    padding: 10px 40px;
    // min-width: 190px;
    height: 60px;
    background: #f5f5f5;
    border-radius: 40px;
    font-size: 30px;
    line-height: 42px;
    color: #333333;
    border: none;

    &.active {
      background: #3b93fb;
      color: #ffffff;
    }
  }
}

.query-search {
  margin-bottom: 20px;

  .van-cell {
    border-radius: 40px;
    background-color: #f5f5f5;
  }
}
// 热词
.rc-box {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
}
.rc-box:after {
  content: '';
  flex: auto;
}
.rc-item {
  height: 56px;
  background: #fff3dd;
  border-radius: 40px;
  font-size: 30px;
  font-weight: bold;
  color: #ff4f00;
  text-align: center;
  line-height: 56px;
  display: inline-block;
  padding: 0 30px;
  margin: 24px 12px 0 12px;
}
/*报告详情页内容css*/
.desc-box {
  padding: 30px 40px;
  font-size: 30px;
  color: #333333;
  text-align: justify;
  line-height: 50px;
  white-space: pre-line;
}
.desc-box__author {
  margin-top: 15px;
  margin-bottom: 15px;
  font-size: 26px;
  color: #999999;
  text-align: center;
  line-height: 26px;
}
// 消息、舆情详情页
.desc-box__content {
  font-size: 30px;
  color: #333333;
  text-align: justify;
  line-height: 50px;
  word-break: break-all;
}
.desc-box__footer {
  display: flex;
  justify-content: space-between;
  margin-top: 30px;
  font-size: 26px;
  color: #999999;
  text-align: center;
  line-height: 26px;
}
// 舆情
.senti-card {
  background: #ffffff;
  box-shadow: $card-box-shadow;
  border-radius: $card-border-radius;
  margin: 30px;
  padding: 30px;

  &__source {
    font-size: 26px;
    color: #999999;
    margin-bottom: 20px;
  }
  &__title {
    font-size: 30px;
    line-height: 42px;
    color: #333333;
    margin-bottom: 20px;
  }
  &__footer {
    margin-top: 14px;
    font-size: 26px;
    color: #999999;
    line-height: 26px;
  }
  &__footer-media {
    margin-right: 10px;
  }
}
// 数据报告
.dataReport-box {
  background: #ffffff;
  box-shadow: 0px 4px 14px 0px rgba(0, 0, 0, 0.11);
  border-radius: 8px;
  margin: 30px;
  padding: 30px 0 24px 30px;
}
.dataReport-title {
  display: flex;
  margin-bottom: 20px;
}
.dataReportIcon {
  width: 40px;
  height: 40px;
  margin-right: 20px;
}
.dataReport-depart {
  font-size: 26px;
  color: #666666;
  letter-spacing: 0;
}
.monitor-name {
  font-size: 30px;
  color: #333333;
  letter-spacing: 0;
  line-height: 38px;
}
.keyWord {
  height: 42px;
  background: #ffead5;
  border-radius: 4px;
  font-size: 26px;
  color: #ff8a15;
  letter-spacing: 0;
  line-height: 26px;
  padding: 8px 14px;
  margin-right: 10px;
  margin-bottom: 14px;
}
.btn {
  width: 140px;
  height: 58px;
  background: #0089fe;
  border-radius: 33px;
  font-size: 26px;
}
.btn-area {
  display: flex;
  justify-content: center;
  align-items: center;
}
.btn-box {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-right: 30px;
}
.report-pop {
  height: 624px;

  &__title {
    font-size: 32px;
    color: #333333;
    text-align: center;
    font-weight: bold;
    margin-top: 30px;
  }
  &__content {
    font-size: 30px;
    color: #333333;
    text-align: justify;
    line-height: 46px;
    margin: 20px 50px 60px;
    height: 322px;
    overflow: auto;
  }
  &__btn {
    width: 300px;
    height: 80px;
    margin: 0 auto;
    display: block;
    font-size: 32px;
    border-radius: 40px;
  }
}
.under-search-container {
  margin-top: 30px;
}
// 专题网格相关
.module-box {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-gap: 20px 18px;
  padding: 0 30px;
}
.keywords-box {
  width: 218px;
  height: 50px;
  line-height: 50px;
  border: 1px solid #0089fe;
  border-radius: 8px;
  font-size: 28px;
  color: #0089fe;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
}
.keyWordsIcon {
  width: 40px;
  margin-right: 10px;
}
.keyWordsIcon-box {
  height: 100%;
  display: flex;
  align-items: center;
}
.show-icon-box {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 20px;
  // margin-left: 338px;
}
.show-icon {
  width: 24px;
  height: 024px;
}
.bottom-separator {
  width: 100%;
  height: 0.5px;
  margin: 20px auto;
  background-color: #777676;
}
.pad-show {
  display: none;
}
.van-divider {
  margin: 24px 0 !important;
}
:root {
  --base-size: 10px;
}
.ai-dialog {
  height: auto;
  width: 80vw;
  border-radius: 4vw;
  .van-dialog__header {
    min-height: 130px;
    background: url('@/assets/img/aiDialog-bg.png') no-repeat center center / 100% 100%;
    font-family: PingFangSC, PingFang SC;
    font-weight: 600;
    font-size: 36px;
    color: #212121;
    line-height: 50px;
    text-align: center;
    font-style: normal;
  }
  .van-dialog__content {
    height: 60vh;
    padding: 1vw;
    overflow-y: auto;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 28px;
    color: #444444;
    line-height: 48px;
    text-align: left;
    font-style: normal;
  }
  .van-button {
    height: 6vh;
    font-size: 3vw;
  }
}

@media (min-width: 600px) {
  :root {
    --base-size: 5px;
  }
}
@media screen and (min-width: 600px) {
  .pad-show {
    display: block;
  }
  .pad-hidden {
    display: none !important;
  }
  .column-wrapper {
    column-count: 2;
    column-gap: 40px;
    column-rule-width: 1px;
    column-rule-style: solid;
    column-rule-color: hsl(0deg 0% 70%);

    > div {
      break-inside: avoid;
    }
  }
  .van-divider {
    margin: 20px 0 !important;
  }
  .topic-page__card {
    .top-container {
      .grid-container {
        gap: 15px;

        .item-container {
          width: 100%;
          height: 130px;
          padding-top: 10px;
          padding-bottom: 10px;
          background: url('@/assets/img/item-container-bigger.png') no-repeat;
          background-size: 100% 100%;

          .title {
            font-size: 16px;
            line-height: 1;
          }
          .center-row {
            .num {
              font-size: 26px;
              line-height: 1;
            }
            .unit {
              font-size: 14px;
              line-height: 18px;
            }
          }

          .bottom-row {
            .rate {
              font-size: 26px;
              line-height: 1;
            }
            .unit {
              margin-left: 7px;
              font-size: 14px;
              line-height: 18px;
            }
            .text-desc {
              font-size: 14px;
              line-height: 18px;
            }
            .desc {
              font-size: 14px;
              line-height: 18px;
              text-align: center;
            }
            .special-rate {
              margin-left: 5px;
              font-size: 26px;
              line-height: 1;
            }
          }

          .left-content {
            .title {
              text-align: center;
              font-size: 16px;
              line-height: 1.2;
            }
            .num {
              font-size: 26px;
              line-height: 1;
            }
            .rate {
              font-size: 26px;
              line-height: 18px;
            }
            .unit {
              font-size: 14px;
              line-height: 18px;
            }
          }
        }
      }
    }
  }

  .dataSummaryBox {
    .leftContentBox {
      flex: 1.7;
    }
    .rightContentBox {
      flex: 1;
      justify-content: end;
    }
    .dataSummayItemBox {
      width: 100%;
      padding: 10px 15px;
      border: none;
      border-radius: 4px;
      background-size: 118% 178%;
      margin: 13px 0;
      .right-value {
        .num {
          font-size: 26px;
          line-height: 30px;
          text-shadow: 0 16px 32px rgba(192, 199, 218, 0.35);
        }
        .unit {
          margin-left: 8px;
          font-size: 14px;
          line-height: 18px;
          text-shadow: 0 16px 32px rgba(192, 199, 218, 0.35);
        }
      }

      .text {
        font-size: 18px;
        line-height: 1.25;
        text-shadow: 0 16px 32px rgba(192, 199, 218, 0.35);
      }
      .icon {
        width: 60px;
        height: 60px;
        margin-right: 9px;
      }
    }
  }
}
